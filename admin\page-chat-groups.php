<?php
/**
 * Chat Groups Admin Page
 *
 * Admin interface for managing chat groups
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_group':
                handle_create_group();
                break;
            case 'update_group':
                handle_update_group();
                break;
            case 'delete_group':
                handle_delete_group();
                break;
            case 'add_member':
                handle_add_member();
                break;
            case 'remove_member':
                handle_remove_member();
                break;
        }
    }
}

// Handle actions
function handle_create_group() {
    if (!wp_verify_nonce($_POST['nonce'], 'dab_chat_groups_nonce')) {
        wp_die('Security check failed');
    }

    $name = sanitize_text_field($_POST['name']);
    $description = sanitize_textarea_field($_POST['description']);
    $group_type = sanitize_text_field($_POST['group_type']);
    $max_members = intval($_POST['max_members']);
    $created_by = get_current_user_id();

    $result = DAB_Chat_Groups_Manager::create_group($name, $description, $created_by, $group_type, $max_members);

    if (is_wp_error($result)) {
        add_action('admin_notices', function() use ($result) {
            echo '<div class="notice notice-error"><p>' . esc_html($result->get_error_message()) . '</p></div>';
        });
    } else {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success"><p>Group created successfully!</p></div>';
        });
    }
}

function handle_update_group() {
    if (!wp_verify_nonce($_POST['nonce'], 'dab_chat_groups_nonce')) {
        wp_die('Security check failed');
    }

    $group_id = intval($_POST['group_id']);
    $data = array(
        'name' => sanitize_text_field($_POST['name']),
        'description' => sanitize_textarea_field($_POST['description']),
        'max_members' => intval($_POST['max_members'])
    );

    $result = DAB_Chat_Groups_Manager::update_group($group_id, $data);

    if ($result) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success"><p>Group updated successfully!</p></div>';
        });
    } else {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p>Failed to update group.</p></div>';
        });
    }
}

function handle_delete_group() {
    if (!wp_verify_nonce($_POST['nonce'], 'dab_chat_groups_nonce')) {
        wp_die('Security check failed');
    }

    $group_id = intval($_POST['group_id']);
    $result = DAB_Chat_Groups_Manager::delete_group($group_id);

    if ($result) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success"><p>Group deleted successfully!</p></div>';
        });
    } else {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p>Failed to delete group.</p></div>';
        });
    }
}

function handle_add_member() {
    if (!wp_verify_nonce($_POST['nonce'], 'dab_chat_groups_nonce')) {
        wp_die('Security check failed');
    }

    $group_id = intval($_POST['group_id']);
    $user_id = intval($_POST['user_id']);
    $role = sanitize_text_field($_POST['role']);

    $result = DAB_Chat_Groups_Manager::add_member($group_id, $user_id, $role);

    if (is_wp_error($result)) {
        add_action('admin_notices', function() use ($result) {
            echo '<div class="notice notice-error"><p>' . esc_html($result->get_error_message()) . '</p></div>';
        });
    } else {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success"><p>Member added successfully!</p></div>';
        });
    }
}

function handle_remove_member() {
    if (!wp_verify_nonce($_POST['nonce'], 'dab_chat_groups_nonce')) {
        wp_die('Security check failed');
    }

    $group_id = intval($_POST['group_id']);
    $user_id = intval($_POST['user_id']);

    $result = DAB_Chat_Groups_Manager::remove_member($group_id, $user_id);

    if ($result) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success"><p>Member removed successfully!</p></div>';
        });
    } else {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p>Failed to remove member.</p></div>';
        });
    }
}

// Get all groups
global $wpdb;
$groups_table = $wpdb->prefix . 'dab_chat_groups';
$groups = $wpdb->get_results("SELECT * FROM $groups_table WHERE is_active = 1 ORDER BY created_at DESC");

// Get frontend users for member selection
$users_table = $wpdb->prefix . 'dab_frontend_users';
$frontend_users = $wpdb->get_results("SELECT id, username, first_name, last_name, email FROM $users_table WHERE status = 'active' ORDER BY username ASC");

$current_group = null;
if (isset($_GET['group_id'])) {
    $group_id = intval($_GET['group_id']);
    $current_group = DAB_Chat_Groups_Manager::get_group_by_id($group_id);
    if ($current_group) {
        $group_members = DAB_Chat_Groups_Manager::get_group_members($group_id);
    }
}
?>

<div class="wrap">
    <h1 class="wp-heading-inline">Chat Groups Management</h1>

    <?php if (!$current_group): ?>
    <a href="#" class="page-title-action" onclick="showCreateGroupModal()">Add New Group</a>
    <?php else: ?>
    <a href="<?php echo admin_url('admin.php?page=dab_chat_groups'); ?>" class="page-title-action">← Back to Groups</a>
    <?php endif; ?>

    <hr class="wp-header-end">

    <?php if (!$current_group): ?>
    <!-- Groups List -->
    <div class="dab-groups-list">
        <?php if (empty($groups)): ?>
        <div class="dab-empty-state">
            <h3>No chat groups found</h3>
            <p>Create your first chat group to get started.</p>
            <button type="button" class="button button-primary" onclick="showCreateGroupModal()">Create Group</button>
        </div>
        <?php else: ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>Group Name</th>
                    <th>Type</th>
                    <th>Members</th>
                    <th>Created By</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($groups as $group): ?>
                <?php
                $member_count = DAB_Chat_Groups_Manager::get_member_count($group->id);
                $creator = get_userdata($group->created_by);
                ?>
                <tr>
                    <td>
                        <strong><?php echo esc_html($group->name); ?></strong>
                        <?php if ($group->description): ?>
                        <br><small class="description"><?php echo esc_html($group->description); ?></small>
                        <?php endif; ?>
                    </td>
                    <td>
                        <span class="dab-group-type dab-group-type-<?php echo esc_attr($group->group_type); ?>">
                            <?php echo esc_html(ucfirst($group->group_type)); ?>
                        </span>
                    </td>
                    <td><?php echo $member_count; ?> / <?php echo $group->max_members; ?></td>
                    <td><?php echo $creator ? esc_html($creator->display_name) : 'Unknown'; ?></td>
                    <td><?php echo date('M j, Y', strtotime($group->created_at)); ?></td>
                    <td>
                        <a href="<?php echo admin_url('admin.php?page=dab_chat_groups&group_id=' . $group->id); ?>" class="button button-small">Manage</a>
                        <button type="button" class="button button-small" onclick="editGroup(<?php echo $group->id; ?>, '<?php echo esc_js($group->name); ?>', '<?php echo esc_js($group->description); ?>', <?php echo $group->max_members; ?>)">Edit</button>
                        <button type="button" class="button button-small button-link-delete" onclick="deleteGroup(<?php echo $group->id; ?>, '<?php echo esc_js($group->name); ?>')">Delete</button>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php endif; ?>
    </div>

    <?php else: ?>
    <!-- Group Management -->
    <div class="dab-group-management">
        <div class="dab-group-header">
            <h2><?php echo esc_html($current_group->name); ?></h2>
            <div class="dab-group-meta">
                <span class="dab-group-type dab-group-type-<?php echo esc_attr($current_group->group_type); ?>">
                    <?php echo esc_html(ucfirst($current_group->group_type)); ?>
                </span>
                <span class="dab-group-members"><?php echo count($group_members); ?> / <?php echo $current_group->max_members; ?> members</span>
            </div>
        </div>

        <?php if ($current_group->description): ?>
        <p class="dab-group-description"><?php echo esc_html($current_group->description); ?></p>
        <?php endif; ?>

        <div class="dab-group-actions">
            <button type="button" class="button" onclick="editGroup(<?php echo $current_group->id; ?>, '<?php echo esc_js($current_group->name); ?>', '<?php echo esc_js($current_group->description); ?>', <?php echo $current_group->max_members; ?>)">Edit Group</button>
            <button type="button" class="button" onclick="showAddMemberModal(<?php echo $current_group->id; ?>)">Add Member</button>
        </div>

        <!-- Members List -->
        <h3>Group Members</h3>
        <?php if (empty($group_members)): ?>
        <p>No members in this group yet.</p>
        <?php else: ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>User</th>
                    <th>Role</th>
                    <th>Joined</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($group_members as $member): ?>
                <tr>
                    <td>
                        <strong><?php echo esc_html($member->first_name . ' ' . $member->last_name); ?></strong>
                        <br><small>@<?php echo esc_html($member->username); ?></small>
                    </td>
                    <td>
                        <span class="dab-member-role dab-member-role-<?php echo esc_attr($member->role); ?>">
                            <?php echo esc_html(ucfirst($member->role)); ?>
                        </span>
                    </td>
                    <td><?php echo date('M j, Y', strtotime($member->joined_at)); ?></td>
                    <td>
                        <button type="button" class="button button-small button-link-delete" onclick="removeMember(<?php echo $current_group->id; ?>, <?php echo $member->user_id; ?>, '<?php echo esc_js($member->username); ?>')">Remove</button>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>

<!-- Create/Edit Group Modal -->
<div id="group-modal" class="dab-modal" style="display: none;">
    <div class="dab-modal-content">
        <div class="dab-modal-header">
            <h3 id="group-modal-title">Create New Group</h3>
            <button type="button" class="dab-modal-close" onclick="closeModal('group-modal')">&times;</button>
        </div>
        <div class="dab-modal-body">
            <form id="group-form" method="post">
                <?php wp_nonce_field('dab_chat_groups_nonce', 'nonce'); ?>
                <input type="hidden" name="action" value="create_group">
                <input type="hidden" name="group_id" id="group-id" value="">

                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="group-name">Group Name <span class="required">*</span></label></th>
                        <td><input type="text" id="group-name" name="name" class="regular-text" required></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="group-description">Description</label></th>
                        <td><textarea id="group-description" name="description" class="large-text" rows="3"></textarea></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="group-type">Group Type</label></th>
                        <td>
                            <select id="group-type" name="group_type">
                                <option value="public">Public</option>
                                <option value="private">Private</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="max-members">Max Members</label></th>
                        <td><input type="number" id="max-members" name="max_members" value="100" min="2" max="1000" class="small-text"></td>
                    </tr>
                </table>

                <p class="submit">
                    <button type="submit" class="button button-primary">Save Group</button>
                    <button type="button" class="button" onclick="closeModal('group-modal')">Cancel</button>
                </p>
            </form>
        </div>
    </div>
</div>

<!-- Add Member Modal -->
<div id="member-modal" class="dab-modal" style="display: none;">
    <div class="dab-modal-content">
        <div class="dab-modal-header">
            <h3>Add Member to Group</h3>
            <button type="button" class="dab-modal-close" onclick="closeModal('member-modal')">&times;</button>
        </div>
        <div class="dab-modal-body">
            <form method="post">
                <?php wp_nonce_field('dab_chat_groups_nonce', 'nonce'); ?>
                <input type="hidden" name="action" value="add_member">
                <input type="hidden" name="group_id" id="member-group-id" value="">

                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="member-user">User <span class="required">*</span></label></th>
                        <td>
                            <select id="member-user" name="user_id" required>
                                <option value="">Select a user...</option>
                                <?php foreach ($frontend_users as $user): ?>
                                <option value="<?php echo $user->id; ?>">
                                    <?php echo esc_html($user->first_name . ' ' . $user->last_name . ' (@' . $user->username . ')'); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="member-role">Role</label></th>
                        <td>
                            <select id="member-role" name="role">
                                <option value="member">Member</option>
                                <option value="admin">Admin</option>
                            </select>
                        </td>
                    </tr>
                </table>

                <p class="submit">
                    <button type="submit" class="button button-primary">Add Member</button>
                    <button type="button" class="button" onclick="closeModal('member-modal')">Cancel</button>
                </p>
            </form>
        </div>
    </div>
</div>

<style>
.dab-empty-state {
    text-align: center;
    padding: 60px 20px;
    background: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
}

.dab-empty-state h3 {
    margin-top: 0;
    color: #666;
}

.dab-group-type {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.dab-group-type-public {
    background: #d4edda;
    color: #155724;
}

.dab-group-type-private {
    background: #f8d7da;
    color: #721c24;
}

.dab-member-role {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.dab-member-role-admin {
    background: #cce5ff;
    color: #004085;
}

.dab-member-role-member {
    background: #e2e3e5;
    color: #383d41;
}

.dab-group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e5e5;
}

.dab-group-meta {
    display: flex;
    gap: 15px;
    align-items: center;
}

.dab-group-description {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.dab-group-actions {
    margin-bottom: 30px;
}

.dab-group-actions .button {
    margin-right: 10px;
}

.dab-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.dab-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 4px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.dab-modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dab-modal-header h3 {
    margin: 0;
}

.dab-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.dab-modal-close:hover {
    color: #333;
}

.dab-modal-body {
    padding: 25px;
}

.required {
    color: #dc3232;
}
</style>

<script>
function showCreateGroupModal() {
    document.getElementById('group-modal-title').textContent = 'Create New Group';
    document.getElementById('group-form').action.value = 'create_group';
    document.getElementById('group-id').value = '';
    document.getElementById('group-name').value = '';
    document.getElementById('group-description').value = '';
    document.getElementById('group-type').value = 'public';
    document.getElementById('max-members').value = '100';
    document.getElementById('group-modal').style.display = 'block';
}

function editGroup(id, name, description, maxMembers) {
    document.getElementById('group-modal-title').textContent = 'Edit Group';
    document.getElementById('group-form').action.value = 'update_group';
    document.getElementById('group-id').value = id;
    document.getElementById('group-name').value = name;
    document.getElementById('group-description').value = description;
    document.getElementById('max-members').value = maxMembers;
    document.getElementById('group-modal').style.display = 'block';
}

function deleteGroup(id, name) {
    if (confirm('Are you sure you want to delete the group "' + name + '"? This action cannot be undone.')) {
        var form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type="hidden" name="action" value="delete_group">' +
                        '<input type="hidden" name="group_id" value="' + id + '">' +
                        '<input type="hidden" name="nonce" value="<?php echo wp_create_nonce('dab_chat_groups_nonce'); ?>">';
        document.body.appendChild(form);
        form.submit();
    }
}

function showAddMemberModal(groupId) {
    document.getElementById('member-group-id').value = groupId;
    document.getElementById('member-user').value = '';
    document.getElementById('member-role').value = 'member';
    document.getElementById('member-modal').style.display = 'block';
}

function removeMember(groupId, userId, username) {
    if (confirm('Are you sure you want to remove "' + username + '" from this group?')) {
        var form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type="hidden" name="action" value="remove_member">' +
                        '<input type="hidden" name="group_id" value="' + groupId + '">' +
                        '<input type="hidden" name="user_id" value="' + userId + '">' +
                        '<input type="hidden" name="nonce" value="<?php echo wp_create_nonce('dab_chat_groups_nonce'); ?>">';
        document.body.appendChild(form);
        form.submit();
    }
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    var modals = document.getElementsByClassName('dab-modal');
    for (var i = 0; i < modals.length; i++) {
        if (event.target == modals[i]) {
            modals[i].style.display = 'none';
        }
    }
}
</script>

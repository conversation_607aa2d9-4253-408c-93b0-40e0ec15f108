<?php
/**
 * Chat Groups Manager
 *
 * Handles chat groups, group membership, and group administration
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_Chat_Groups_Manager {

    /**
     * Initialize the class
     */
    public static function init() {
        // Create tables if they don't exist
        self::create_tables();

        // Register AJAX handlers
        add_action('wp_ajax_dab_create_group', array(__CLASS__, 'ajax_create_group'));
        add_action('wp_ajax_nopriv_dab_create_group', array(__CLASS__, 'ajax_create_group'));

        add_action('wp_ajax_dab_get_groups', array(__CLASS__, 'ajax_get_groups'));
        add_action('wp_ajax_nopriv_dab_get_groups', array(__CLASS__, 'ajax_get_groups'));

        add_action('wp_ajax_dab_join_group', array(__CLASS__, 'ajax_join_group'));
        add_action('wp_ajax_nopriv_dab_join_group', array(__CLASS__, 'ajax_join_group'));

        add_action('wp_ajax_dab_leave_group', array(__CLASS__, 'ajax_leave_group'));
        add_action('wp_ajax_nopriv_dab_leave_group', array(__CLASS__, 'ajax_leave_group'));

        add_action('wp_ajax_dab_add_group_member', array(__CLASS__, 'ajax_add_group_member'));
        add_action('wp_ajax_nopriv_dab_add_group_member', array(__CLASS__, 'ajax_add_group_member'));

        add_action('wp_ajax_dab_remove_group_member', array(__CLASS__, 'ajax_remove_group_member'));
        add_action('wp_ajax_nopriv_dab_remove_group_member', array(__CLASS__, 'ajax_remove_group_member'));

        add_action('wp_ajax_dab_update_group', array(__CLASS__, 'ajax_update_group'));
        add_action('wp_ajax_nopriv_dab_update_group', array(__CLASS__, 'ajax_update_group'));

        add_action('wp_ajax_dab_delete_group', array(__CLASS__, 'ajax_delete_group'));
        add_action('wp_ajax_nopriv_dab_delete_group', array(__CLASS__, 'ajax_delete_group'));

        add_action('wp_ajax_dab_get_group_members', array(__CLASS__, 'ajax_get_group_members'));
        add_action('wp_ajax_nopriv_dab_get_group_members', array(__CLASS__, 'ajax_get_group_members'));
    }

    /**
     * Create necessary tables for chat groups
     */
    public static function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Chat groups table
        $groups_table = $wpdb->prefix . 'dab_chat_groups';
        $sql_groups = "CREATE TABLE IF NOT EXISTS $groups_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            description TEXT NULL,
            avatar_url VARCHAR(500) NULL,
            group_type VARCHAR(20) DEFAULT 'public',
            created_by BIGINT(20) UNSIGNED NOT NULL,
            max_members INT DEFAULT 100,
            is_active TINYINT(1) DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY created_by (created_by),
            KEY group_type (group_type),
            KEY is_active (is_active)
        ) $charset_collate;";

        // Group members table
        $members_table = $wpdb->prefix . 'dab_chat_group_members';
        $sql_members = "CREATE TABLE IF NOT EXISTS $members_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            group_id BIGINT(20) UNSIGNED NOT NULL,
            user_id BIGINT(20) UNSIGNED NOT NULL,
            role VARCHAR(20) DEFAULT 'member',
            joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_read_message_id BIGINT(20) UNSIGNED NULL,
            is_muted TINYINT(1) DEFAULT 0,
            PRIMARY KEY (id),
            UNIQUE KEY unique_membership (group_id, user_id),
            KEY group_id (group_id),
            KEY user_id (user_id),
            KEY role (role)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_groups);
        dbDelta($sql_members);
    }

    /**
     * Create a new chat group
     */
    public static function create_group($name, $description, $created_by, $group_type = 'public', $max_members = 100) {
        global $wpdb;

        if (empty($name)) {
            return new WP_Error('empty_name', 'Group name is required.');
        }

        // Check if user exists
        if (!self::user_exists($created_by)) {
            return new WP_Error('invalid_user', 'Invalid user.');
        }

        $groups_table = $wpdb->prefix . 'dab_chat_groups';

        $result = $wpdb->insert($groups_table, array(
            'name' => sanitize_text_field($name),
            'description' => sanitize_textarea_field($description),
            'group_type' => $group_type,
            'created_by' => $created_by,
            'max_members' => $max_members,
            'created_at' => current_time('mysql')
        ));

        if ($result === false) {
            return new WP_Error('create_failed', 'Failed to create group.');
        }

        $group_id = $wpdb->insert_id;

        // Add creator as admin
        self::add_member($group_id, $created_by, 'admin');

        return $group_id;
    }

    /**
     * Get groups for a user
     */
    public static function get_user_groups($user_id, $limit = 20) {
        global $wpdb;

        $groups_table = $wpdb->prefix . 'dab_chat_groups';
        $members_table = $wpdb->prefix . 'dab_chat_group_members';
        $messages_table = $wpdb->prefix . 'dab_chat_messages';

        $query = "
            SELECT g.*,
                   gm.role as user_role,
                   gm.is_muted,
                   gm.last_read_message_id,
                   (SELECT COUNT(*) FROM $members_table WHERE group_id = g.id) as member_count,
                   (SELECT COUNT(*) FROM $messages_table
                    WHERE group_id = g.id AND id > COALESCE(gm.last_read_message_id, 0)
                    AND sender_id != %d AND is_deleted = 0) as unread_count,
                   (SELECT message FROM $messages_table
                    WHERE group_id = g.id AND is_deleted = 0
                    ORDER BY created_at DESC LIMIT 1) as last_message
            FROM $groups_table g
            INNER JOIN $members_table gm ON g.id = gm.group_id
            WHERE gm.user_id = %d AND g.is_active = 1
            ORDER BY g.updated_at DESC
            LIMIT %d
        ";

        return $wpdb->get_results($wpdb->prepare($query, $user_id, $user_id, $limit));
    }

    /**
     * Get all public groups
     */
    public static function get_public_groups($user_id, $limit = 20, $search = '') {
        global $wpdb;

        $groups_table = $wpdb->prefix . 'dab_chat_groups';
        $members_table = $wpdb->prefix . 'dab_chat_group_members';

        $where_conditions = array("g.group_type = 'public'", "g.is_active = 1");
        $where_values = array();

        if (!empty($search)) {
            $where_conditions[] = "(g.name LIKE %s OR g.description LIKE %s)";
            $search_term = '%' . $wpdb->esc_like($search) . '%';
            $where_values = array_merge($where_values, array($search_term, $search_term));
        }

        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

        $query = "
            SELECT g.*,
                   (SELECT COUNT(*) FROM $members_table WHERE group_id = g.id) as member_count,
                   (SELECT role FROM $members_table WHERE group_id = g.id AND user_id = %d) as user_role
            FROM $groups_table g
            $where_clause
            ORDER BY g.created_at DESC
            LIMIT %d
        ";

        $where_values = array_merge(array($user_id), $where_values, array($limit));
        return $wpdb->get_results($wpdb->prepare($query, $where_values));
    }

    /**
     * Add a member to a group
     */
    public static function add_member($group_id, $user_id, $role = 'member') {
        global $wpdb;

        // Check if group exists and is active
        $group = self::get_group_by_id($group_id);
        if (!$group || !$group->is_active) {
            return new WP_Error('invalid_group', 'Group not found or inactive.');
        }

        // Check if user exists
        if (!self::user_exists($user_id)) {
            return new WP_Error('invalid_user', 'User not found.');
        }

        // Check if group is full
        $current_members = self::get_member_count($group_id);
        if ($current_members >= $group->max_members) {
            return new WP_Error('group_full', 'Group has reached maximum member limit.');
        }

        $members_table = $wpdb->prefix . 'dab_chat_group_members';

        $result = $wpdb->insert($members_table, array(
            'group_id' => $group_id,
            'user_id' => $user_id,
            'role' => $role,
            'joined_at' => current_time('mysql')
        ));

        if ($result === false) {
            return new WP_Error('add_failed', 'Failed to add member to group.');
        }

        return true;
    }

    /**
     * Remove a member from a group
     */
    public static function remove_member($group_id, $user_id) {
        global $wpdb;

        $members_table = $wpdb->prefix . 'dab_chat_group_members';

        $result = $wpdb->delete($members_table, array(
            'group_id' => $group_id,
            'user_id' => $user_id
        ));

        return $result !== false;
    }

    /**
     * Check if user is member of group
     */
    public static function is_member($group_id, $user_id) {
        global $wpdb;

        $members_table = $wpdb->prefix . 'dab_chat_group_members';

        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $members_table WHERE group_id = %d AND user_id = %d",
            $group_id, $user_id
        ));

        return $count > 0;
    }

    /**
     * Check if user is admin of group
     */
    public static function is_admin($group_id, $user_id) {
        global $wpdb;

        $members_table = $wpdb->prefix . 'dab_chat_group_members';

        $role = $wpdb->get_var($wpdb->prepare(
            "SELECT role FROM $members_table WHERE group_id = %d AND user_id = %d",
            $group_id, $user_id
        ));

        return $role === 'admin';
    }

    /**
     * Get group by ID
     */
    public static function get_group_by_id($group_id) {
        global $wpdb;

        $groups_table = $wpdb->prefix . 'dab_chat_groups';

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $groups_table WHERE id = %d",
            $group_id
        ));
    }

    /**
     * Get group members
     */
    public static function get_group_members($group_id, $limit = 50) {
        global $wpdb;

        $members_table = $wpdb->prefix . 'dab_chat_group_members';
        $users_table = $wpdb->prefix . 'dab_frontend_users';

        return $wpdb->get_results($wpdb->prepare(
            "SELECT gm.*, u.username, u.first_name, u.last_name, u.avatar_url
             FROM $members_table gm
             LEFT JOIN $users_table u ON gm.user_id = u.id
             WHERE gm.group_id = %d
             ORDER BY gm.role DESC, gm.joined_at ASC
             LIMIT %d",
            $group_id, $limit
        ));
    }

    /**
     * Get member count
     */
    public static function get_member_count($group_id) {
        global $wpdb;

        $members_table = $wpdb->prefix . 'dab_chat_group_members';

        return $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $members_table WHERE group_id = %d",
            $group_id
        ));
    }

    /**
     * Update group
     */
    public static function update_group($group_id, $data) {
        global $wpdb;

        $groups_table = $wpdb->prefix . 'dab_chat_groups';

        $update_data = array();

        if (isset($data['name'])) {
            $update_data['name'] = sanitize_text_field($data['name']);
        }

        if (isset($data['description'])) {
            $update_data['description'] = sanitize_textarea_field($data['description']);
        }

        if (isset($data['avatar_url'])) {
            $update_data['avatar_url'] = esc_url_raw($data['avatar_url']);
        }

        if (isset($data['max_members'])) {
            $update_data['max_members'] = intval($data['max_members']);
        }

        if (!empty($update_data)) {
            $update_data['updated_at'] = current_time('mysql');

            $result = $wpdb->update($groups_table, $update_data, array('id' => $group_id));
            return $result !== false;
        }

        return false;
    }

    /**
     * Delete group
     */
    public static function delete_group($group_id) {
        global $wpdb;

        $groups_table = $wpdb->prefix . 'dab_chat_groups';

        // Soft delete - mark as inactive
        $result = $wpdb->update($groups_table,
            array('is_active' => 0),
            array('id' => $group_id)
        );

        return $result !== false;
    }

    /**
     * Check if user exists
     */
    private static function user_exists($user_id) {
        global $wpdb;

        $users_table = $wpdb->prefix . 'dab_frontend_users';
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $users_table WHERE id = %d AND status = 'active'",
            $user_id
        ));

        return $count > 0;
    }

    /**
     * AJAX handler for creating groups
     */
    public static function ajax_create_group() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in to create groups');
            return;
        }

        $name = sanitize_text_field($_POST['name'] ?? '');
        $description = sanitize_textarea_field($_POST['description'] ?? '');
        $group_type = sanitize_text_field($_POST['group_type'] ?? 'public');
        $max_members = intval($_POST['max_members'] ?? 100);

        if (empty($name)) {
            wp_send_json_error('Group name is required');
            return;
        }

        $result = self::create_group($name, $description, $current_user->id, $group_type, $max_members);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array(
                'message' => 'Group created successfully',
                'group_id' => $result
            ));
        }
    }

    /**
     * AJAX handler for getting groups
     */
    public static function ajax_get_groups() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $type = sanitize_text_field($_POST['type'] ?? 'user'); // 'user' or 'public'
        $limit = intval($_POST['limit'] ?? 20);
        $search = sanitize_text_field($_POST['search'] ?? '');

        if ($type === 'public') {
            $groups = self::get_public_groups($current_user->id, $limit, $search);
        } else {
            $groups = self::get_user_groups($current_user->id, $limit);
        }

        wp_send_json_success($groups);
    }

    /**
     * AJAX handler for joining groups
     */
    public static function ajax_join_group() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $group_id = intval($_POST['group_id'] ?? 0);
        if (!$group_id) {
            wp_send_json_error('Group ID is required');
            return;
        }

        // Check if already a member
        if (self::is_member($group_id, $current_user->id)) {
            wp_send_json_error('You are already a member of this group');
            return;
        }

        $result = self::add_member($group_id, $current_user->id);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success('Successfully joined the group');
        }
    }

    /**
     * AJAX handler for leaving groups
     */
    public static function ajax_leave_group() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $group_id = intval($_POST['group_id'] ?? 0);
        if (!$group_id) {
            wp_send_json_error('Group ID is required');
            return;
        }

        $result = self::remove_member($group_id, $current_user->id);

        if ($result) {
            wp_send_json_success('Successfully left the group');
        } else {
            wp_send_json_error('Failed to leave the group');
        }
    }

    /**
     * AJAX handler for adding group members
     */
    public static function ajax_add_group_member() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $group_id = intval($_POST['group_id'] ?? 0);
        $user_id = intval($_POST['user_id'] ?? 0);
        $role = sanitize_text_field($_POST['role'] ?? 'member');

        if (!$group_id || !$user_id) {
            wp_send_json_error('Group ID and User ID are required');
            return;
        }

        // Check if current user is admin of the group
        if (!self::is_admin($group_id, $current_user->id)) {
            wp_send_json_error('You do not have permission to add members to this group');
            return;
        }

        $result = self::add_member($group_id, $user_id, $role);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success('Member added successfully');
        }
    }

    /**
     * AJAX handler for removing group members
     */
    public static function ajax_remove_group_member() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $group_id = intval($_POST['group_id'] ?? 0);
        $user_id = intval($_POST['user_id'] ?? 0);

        if (!$group_id || !$user_id) {
            wp_send_json_error('Group ID and User ID are required');
            return;
        }

        // Check if current user is admin of the group
        if (!self::is_admin($group_id, $current_user->id)) {
            wp_send_json_error('You do not have permission to remove members from this group');
            return;
        }

        $result = self::remove_member($group_id, $user_id);

        if ($result) {
            wp_send_json_success('Member removed successfully');
        } else {
            wp_send_json_error('Failed to remove member');
        }
    }

    /**
     * AJAX handler for updating groups
     */
    public static function ajax_update_group() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $group_id = intval($_POST['group_id'] ?? 0);
        if (!$group_id) {
            wp_send_json_error('Group ID is required');
            return;
        }

        // Check if current user is admin of the group
        if (!self::is_admin($group_id, $current_user->id)) {
            wp_send_json_error('You do not have permission to update this group');
            return;
        }

        $update_data = array();

        if (isset($_POST['name'])) {
            $update_data['name'] = $_POST['name'];
        }

        if (isset($_POST['description'])) {
            $update_data['description'] = $_POST['description'];
        }

        if (isset($_POST['max_members'])) {
            $update_data['max_members'] = $_POST['max_members'];
        }

        $result = self::update_group($group_id, $update_data);

        if ($result) {
            wp_send_json_success('Group updated successfully');
        } else {
            wp_send_json_error('Failed to update group');
        }
    }

    /**
     * AJAX handler for deleting groups
     */
    public static function ajax_delete_group() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $group_id = intval($_POST['group_id'] ?? 0);
        if (!$group_id) {
            wp_send_json_error('Group ID is required');
            return;
        }

        // Check if current user is admin of the group
        if (!self::is_admin($group_id, $current_user->id)) {
            wp_send_json_error('You do not have permission to delete this group');
            return;
        }

        $result = self::delete_group($group_id);

        if ($result) {
            wp_send_json_success('Group deleted successfully');
        } else {
            wp_send_json_error('Failed to delete group');
        }
    }

    /**
     * AJAX handler for getting group members
     */
    public static function ajax_get_group_members() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_chat_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $current_user = DAB_Frontend_User_Manager::get_current_user();
        if (!$current_user) {
            wp_send_json_error('You must be logged in');
            return;
        }

        $group_id = intval($_POST['group_id'] ?? 0);
        if (!$group_id) {
            wp_send_json_error('Group ID is required');
            return;
        }

        // Check if user is member of the group
        if (!self::is_member($group_id, $current_user->id)) {
            wp_send_json_error('You are not a member of this group');
            return;
        }

        $limit = intval($_POST['limit'] ?? 50);
        $members = self::get_group_members($group_id, $limit);

        wp_send_json_success($members);
    }
}

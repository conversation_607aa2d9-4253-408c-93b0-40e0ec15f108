<?php
/**
 * Frontend System Installer
 *
 * Handles automatic creation of required pages and setup
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_Frontend_Installer {

    /**
     * Install frontend pages and setup
     */
    public static function install() {
        self::create_frontend_pages();
        self::set_default_options();
        self::flush_rewrite_rules();
    }

    /**
     * Create required frontend pages
     */
    public static function create_frontend_pages() {
        $pages = array(
            'login' => array(
                'title' => 'Login',
                'content' => '[dab_user_login]',
                'slug' => 'login'
            ),
            'register' => array(
                'title' => 'Register',
                'content' => '[dab_user_register]',
                'slug' => 'register'
            ),
            'user-dashboard' => array(
                'title' => 'User Dashboard',
                'content' => '[dab_user_dashboard]',
                'slug' => 'user-dashboard'
            ),
            'user-profile' => array(
                'title' => 'User Profile',
                'content' => '[dab_user_profile]',
                'slug' => 'user-profile'
            ),
            'reset-password' => array(
                'title' => 'Reset Password',
                'content' => '<p>Please check your email for password reset instructions.</p>',
                'slug' => 'reset-password'
            ),
            'chat' => array(
                'title' => 'Chat',
                'content' => '[dab_chat]',
                'slug' => 'chat'
            )
        );

        foreach ($pages as $page_key => $page_data) {
            // Check if page already exists
            $existing_page = get_page_by_path($page_data['slug']);

            if (!$existing_page) {
                $page_id = wp_insert_post(array(
                    'post_title' => $page_data['title'],
                    'post_content' => $page_data['content'],
                    'post_name' => $page_data['slug'],
                    'post_status' => 'publish',
                    'post_type' => 'page',
                    'post_author' => 1,
                    'comment_status' => 'closed',
                    'ping_status' => 'closed'
                ));

                if ($page_id && !is_wp_error($page_id)) {
                    // Store page ID in options for future reference
                    update_option('dab_frontend_page_' . $page_key, $page_id);

                    // Add meta to identify these as DAB pages
                    update_post_meta($page_id, '_dab_frontend_page', true);
                    update_post_meta($page_id, '_dab_page_type', $page_key);
                }
            } else {
                // Store existing page ID
                update_option('dab_frontend_page_' . $page_key, $existing_page->ID);
            }
        }
    }

    /**
     * Set default options
     */
    public static function set_default_options() {
        $default_options = array(
            'dab_frontend_registration_enabled' => 'yes',
            'dab_frontend_email_verification' => 'yes',
            'dab_frontend_session_timeout' => 24, // hours
            'dab_frontend_remember_me_timeout' => 720, // hours (30 days)
            'dab_frontend_password_min_length' => 6,
            'dab_frontend_default_role' => 'user'
        );

        foreach ($default_options as $option_name => $option_value) {
            if (get_option($option_name) === false) {
                add_option($option_name, $option_value);
            }
        }
    }

    /**
     * Flush rewrite rules
     */
    public static function flush_rewrite_rules() {
        flush_rewrite_rules();
    }

    /**
     * Uninstall frontend system
     */
    public static function uninstall() {
        if (get_option('dab_frontend_delete_pages_on_uninstall') === 'yes') {
            self::delete_frontend_pages();
        }

        self::delete_frontend_options();
    }

    /**
     * Delete frontend pages
     */
    public static function delete_frontend_pages() {
        $page_types = array('login', 'register', 'user-dashboard', 'user-profile', 'reset-password', 'chat');

        foreach ($page_types as $page_type) {
            $page_id = get_option('dab_frontend_page_' . $page_type);
            if ($page_id) {
                wp_delete_post($page_id, true);
                delete_option('dab_frontend_page_' . $page_type);
            }
        }
    }

    /**
     * Delete frontend options
     */
    public static function delete_frontend_options() {
        $options_to_delete = array(
            'dab_frontend_registration_enabled',
            'dab_frontend_email_verification',
            'dab_frontend_session_timeout',
            'dab_frontend_remember_me_timeout',
            'dab_frontend_password_min_length',
            'dab_frontend_default_role',
            'dab_frontend_delete_pages_on_uninstall'
        );

        foreach ($options_to_delete as $option) {
            delete_option($option);
        }
    }

    /**
     * Get frontend page URL
     */
    public static function get_page_url($page_type) {
        $page_id = get_option('dab_frontend_page_' . $page_type);
        if ($page_id) {
            return get_permalink($page_id);
        }
        return home_url('/' . $page_type . '/');
    }

    /**
     * Check if frontend system is properly installed
     */
    public static function is_installed() {
        $required_pages = array('login', 'register', 'user-dashboard', 'user-profile', 'chat');

        foreach ($required_pages as $page_type) {
            $page_id = get_option('dab_frontend_page_' . $page_type);
            if (!$page_id || !get_post($page_id)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get installation status
     */
    public static function get_installation_status() {
        $status = array(
            'pages_created' => 0,
            'pages_total' => 6,
            'missing_pages' => array(),
            'is_complete' => false
        );

        $page_types = array('login', 'register', 'user-dashboard', 'user-profile', 'reset-password', 'chat');

        foreach ($page_types as $page_type) {
            $page_id = get_option('dab_frontend_page_' . $page_type);
            if ($page_id && get_post($page_id)) {
                $status['pages_created']++;
            } else {
                $status['missing_pages'][] = $page_type;
            }
        }

        $status['is_complete'] = ($status['pages_created'] === $status['pages_total']);

        return $status;
    }

    /**
     * Create missing pages
     */
    public static function create_missing_pages() {
        $status = self::get_installation_status();

        if (!empty($status['missing_pages'])) {
            self::create_frontend_pages();
            return true;
        }

        return false;
    }

    /**
     * Add admin notice for installation
     */
    public static function add_admin_notices() {
        if (!self::is_installed()) {
            add_action('admin_notices', array(__CLASS__, 'installation_notice'));
        }
    }

    /**
     * Display installation notice
     */
    public static function installation_notice() {
        $status = self::get_installation_status();
        $install_url = add_query_arg(array(
            'page' => 'dab-settings',
            'tab' => 'frontend',
            'action' => 'install_frontend'
        ), admin_url('admin.php'));

        ?>
        <div class="notice notice-warning is-dismissible">
            <p>
                <strong>DB App Builder Frontend System:</strong>
                <?php echo sprintf(
                    __('%d of %d required pages are missing. ', 'db-app-builder'),
                    count($status['missing_pages']),
                    $status['pages_total']
                ); ?>
                <a href="<?php echo esc_url($install_url); ?>" class="button button-primary">
                    <?php _e('Install Frontend Pages', 'db-app-builder'); ?>
                </a>
            </p>
            <?php if (!empty($status['missing_pages'])): ?>
            <p>
                <small>
                    <?php _e('Missing pages:', 'db-app-builder'); ?>
                    <?php echo implode(', ', $status['missing_pages']); ?>
                </small>
            </p>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * Initialize installer
     */
    public static function init() {
        // Add admin notices
        add_action('admin_init', array(__CLASS__, 'add_admin_notices'));
    }
}
